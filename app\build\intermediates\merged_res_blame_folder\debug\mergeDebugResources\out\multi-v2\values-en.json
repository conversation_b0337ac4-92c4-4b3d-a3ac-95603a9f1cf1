{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.grupo3.medrem.app-mergeDebugResources-30:\\values-en\\values-en.xml", "map": [{"source": "C:\\Users\\<USER>\\Documents\\SISE\\Aplicaciones Moviles\\Examen Final\\am-g3-medrem\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "134,129,1,88,87,53,56,54,55,52,51,82,78,83,84,81,79,80,31,30,28,26,32,27,29,33,25,24,6,7,5,4,60,75,71,63,64,65,66,61,62,72,73,74,67,68,69,70,59,12,11,10,16,15,14,20,19,18,21,40,39,46,45,47,41,43,48,38,44,37,42,36,125,126,113,114,115,116,118,117,123,124,111,112,119,120,121,122,110,2,93,92,95,94,97,96,99,98,101,100,103,102,105,104,107,106,91", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7931,7753,16,4812,4761,2941,3154,3024,3094,2859,2805,4608,4430,4651,4696,4563,4473,4517,1689,1635,1510,1402,1756,1458,1562,1827,1327,1278,276,339,184,133,3311,4337,4061,3544,3599,3674,3735,3387,3460,4124,4188,4270,3809,3872,3947,3998,3251,637,494,417,886,761,691,1143,1015,940,1197,2179,2123,2579,2483,2645,2245,2355,2714,2066,2423,1975,2303,1917,7565,7631,6784,6841,6915,6970,7111,7049,7435,7488,6635,6700,7173,7226,7304,7357,6583,64,5027,4951,5161,5088,5351,5275,5584,5509,5833,5763,6040,5963,6269,6202,6438,6377,4890", "endLines": "137,132,1,88,87,53,56,54,55,52,51,82,78,83,84,81,79,80,31,30,28,26,32,27,29,33,25,24,6,7,5,4,60,75,71,63,64,65,66,61,62,72,73,74,67,68,69,70,59,12,11,10,16,15,14,20,19,18,21,40,39,46,45,47,41,43,48,38,44,37,42,36,125,126,113,114,115,116,118,117,123,124,111,112,119,120,121,122,110,2,93,92,95,94,97,96,99,98,101,100,103,102,105,104,107,106,91", "endColumns": "19,19,47,48,50,82,60,69,59,81,53,42,42,44,42,44,43,45,66,53,51,55,70,51,72,57,74,48,62,52,91,50,75,61,62,54,74,60,73,72,83,63,81,66,62,74,50,62,59,52,142,76,52,124,69,53,127,74,51,65,55,65,95,68,57,67,57,56,59,90,51,57,65,86,56,73,54,78,61,61,52,76,64,83,52,77,52,77,51,67,60,75,113,72,157,75,178,74,129,69,161,76,107,66,112,60,60", "endOffsets": "8035,7921,59,4856,4807,3019,3210,3089,3149,2936,2854,4646,4468,4691,4734,4603,4512,4558,1751,1684,1557,1453,1822,1505,1630,1880,1397,1322,334,387,271,179,3382,4394,4119,3594,3669,3730,3804,3455,3539,4183,4265,4332,3867,3942,3993,4056,3306,685,632,489,934,881,756,1192,1138,1010,1244,2240,2174,2640,2574,2709,2298,2418,2767,2118,2478,2061,2350,1970,7626,7713,6836,6910,6965,7044,7168,7106,7483,7560,6695,6779,7221,7299,7352,7430,6630,127,5083,5022,5270,5156,5504,5346,5758,5579,5958,5828,6197,6035,6372,6264,6546,6433,4946"}, "to": {"startLines": "2,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,337,385,434,485,568,629,699,759,841,895,938,981,1026,1069,1114,1158,1204,1271,1325,1377,1433,1504,1556,1629,1687,1762,1811,1874,1927,2019,2070,2146,2208,2271,2326,2401,2462,2536,2609,2693,2757,2839,2906,2969,3044,3095,3158,3218,3271,3414,3491,3544,3669,3739,3793,3921,3996,4048,4114,4170,4236,4332,4401,4459,4527,4585,4642,4702,4793,4845,4903,4969,5056,5113,5187,5242,5321,5383,5445,5498,5575,5640,5724,5777,5855,5908,5986,6038,6106,6167,6243,6357,6430,6588,6664,6843,6918,7048,7118,7280,7357,7465,7532,7645,7706", "endLines": "5,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114", "endColumns": "19,19,47,48,50,82,60,69,59,81,53,42,42,44,42,44,43,45,66,53,51,55,70,51,72,57,74,48,62,52,91,50,75,61,62,54,74,60,73,72,83,63,81,66,62,74,50,62,59,52,142,76,52,124,69,53,127,74,51,65,55,65,95,68,57,67,57,56,59,90,51,57,65,86,56,73,54,78,61,61,52,76,64,83,52,77,52,77,51,67,60,75,113,72,157,75,178,74,129,69,161,76,107,66,112,60,60", "endOffsets": "159,332,380,429,480,563,624,694,754,836,890,933,976,1021,1064,1109,1153,1199,1266,1320,1372,1428,1499,1551,1624,1682,1757,1806,1869,1922,2014,2065,2141,2203,2266,2321,2396,2457,2531,2604,2688,2752,2834,2901,2964,3039,3090,3153,3213,3266,3409,3486,3539,3664,3734,3788,3916,3991,4043,4109,4165,4231,4327,4396,4454,4522,4580,4637,4697,4788,4840,4898,4964,5051,5108,5182,5237,5316,5378,5440,5493,5570,5635,5719,5772,5850,5903,5981,6033,6101,6162,6238,6352,6425,6583,6659,6838,6913,7043,7113,7275,7352,7460,7527,7640,7701,7762"}}]}, {"outputFile": "com.grupo3.medrem.app-mergeDebugResources-30:/values-en/values-en.xml", "map": [{"source": "C:\\Users\\<USER>\\Documents\\SISE\\Aplicaciones Moviles\\Examen Final\\am-g3-medrem\\app\\src\\main\\res\\values-en\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,61,60,54,55,56,53,64,63,62,57,32,34,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,86,87,-1,89,80,94,90,95,96,93,91,92,85,79,-1,84,76,77,88,78,-1,82,-1,-1,-1,-1,15,14,19,18,17,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,69,68,-1,-1,103,102,105,104,107,106,109,108,111,110,113,112,115,114,-1,101,67", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,4,4,4,4,4,4,4,4,4,4,4,-1,4,4,4,4,4,-1,4,-1,-1,-1,-1,4,4,4,4,4,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4,4,-1,-1,4,4,4,4,4,4,4,4,4,4,4,4,4,4,-1,4,4", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,3518,3470,3030,3102,3174,2954,3705,3655,3572,3261,1768,1906,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4829,4904,-1,5041,4469,5318,5096,5372,5428,5262,5150,5205,4761,4410,-1,4698,4201,4270,4990,4347,-1,4580,-1,-1,-1,-1,757,691,1089,960,886,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3880,3833,-1,-1,5877,5801,6214,6144,6558,6490,6895,6820,7179,7112,7442,7377,7758,7690,-1,5663,3790", "endColumns": "-1,-1,-1,-1,-1,-1,-1,-1,53,47,71,71,86,75,55,49,82,55,70,66,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,74,85,-1,54,59,53,53,55,53,55,54,56,67,58,-1,62,68,76,50,62,-1,56,-1,-1,-1,-1,127,65,57,128,73,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,48,46,-1,-1,266,75,275,69,261,67,216,74,197,66,247,64,212,67,-1,137,42", "endOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,3567,3513,3097,3169,3256,3025,3756,3700,3650,3312,1834,1968,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,4899,4985,-1,5091,4524,5367,5145,5423,5477,5313,5200,5257,4824,4464,-1,4756,4265,4342,5036,4405,-1,4632,-1,-1,-1,-1,880,752,1142,1084,955,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,3924,3875,-1,-1,6139,5872,6485,6209,6815,6553,7107,6890,7372,7174,7685,7437,7966,7753,-1,5796,3828"}, "to": {"startLines": "2,6,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,337,385,434,485,568,629,699,753,801,873,945,1032,1108,1164,1214,1297,1353,1424,1491,1558,1612,1664,1720,1791,1843,1916,1974,2049,2098,2161,2214,2306,2357,2432,2518,2594,2649,2709,2763,2817,2873,2927,2983,3038,3095,3163,3222,3283,3346,3415,3492,3543,3606,3657,3714,3774,3827,3970,4047,4175,4241,4299,4428,4502,4554,4620,4676,4742,4838,4907,4965,5033,5091,5148,5208,5299,5351,5409,5475,5562,5619,5693,5748,5827,5889,5951,6004,6081,6146,6230,6283,6361,6414,6492,6544,6612,6661,6708,6769,6845,7112,7188,7464,7534,7796,7864,8081,8156,8354,8421,8669,8734,8947,9015,9076,9214", "endLines": "5,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125", "endColumns": "19,19,47,48,50,82,60,69,53,47,71,71,86,75,55,49,82,55,70,66,66,53,51,55,70,51,72,57,74,48,62,52,91,50,74,85,75,54,59,53,53,55,53,55,54,56,67,58,60,62,68,76,50,62,50,56,59,52,142,76,127,65,57,128,73,51,65,55,65,95,68,57,67,57,56,59,90,51,57,65,86,56,73,54,78,61,61,52,76,64,83,52,77,52,77,51,67,48,46,60,75,266,75,275,69,261,67,216,74,197,66,247,64,212,67,60,137,42", "endOffsets": "159,332,380,429,480,563,624,694,748,796,868,940,1027,1103,1159,1209,1292,1348,1419,1486,1553,1607,1659,1715,1786,1838,1911,1969,2044,2093,2156,2209,2301,2352,2427,2513,2589,2644,2704,2758,2812,2868,2922,2978,3033,3090,3158,3217,3278,3341,3410,3487,3538,3601,3652,3709,3769,3822,3965,4042,4170,4236,4294,4423,4497,4549,4615,4671,4737,4833,4902,4960,5028,5086,5143,5203,5294,5346,5404,5470,5557,5614,5688,5743,5822,5884,5946,5999,6076,6141,6225,6278,6356,6409,6487,6539,6607,6656,6703,6764,6840,7107,7183,7459,7529,7791,7859,8076,8151,8349,8416,8664,8729,8942,9010,9071,9209,9252"}}]}]}