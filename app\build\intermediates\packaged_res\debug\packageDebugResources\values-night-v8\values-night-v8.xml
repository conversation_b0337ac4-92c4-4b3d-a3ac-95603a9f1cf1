<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background_primary">#121212</color>
    <color name="background_secondary">#1E1E1E</color>
    <color name="black">#FFFFFFFF</color>
    <color name="blue">#0076F6</color>
    <color name="button_cancel">#F44336</color>
    <color name="button_save">#2196F3</color>
    <color name="card_background">#1E1E1E</color>
    <color name="colorMissed">#B71C1C</color>
    <color name="colorPending">#1E1E1E</color>
    <color name="colorTaken">#1B5E20</color>
    <color name="color_error">#F44336</color>
    <color name="color_success">#4CAF50</color>
    <color name="day_button_selected">#9C27B0</color>
    <color name="gris">#BBBBBB</color>
    <color name="iconColor">#00BCD4</color>
    <color name="icon_tint">#FFFFFF</color>
    <color name="surface_color">#1E1E1E</color>
    <color name="text_color_secondary">#AAAAAA</color>
    <color name="text_primary">#FFFFFF</color>
    <color name="text_secondary">#AAAAAA</color>
    <color name="white">#FF121212</color>
    <style name="Base.Theme.MedRem" parent="Theme.Material3.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/blue</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/iconColor</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="colorSurface">#FF1E1E1E</item>
        <item name="colorOnSurface">@color/white</item>
        <item name="android:colorBackground">#FF121212</item>
        <item name="colorOnBackground">@color/white</item>
        <item name="android:windowBackground">#FF121212</item>
        <item name="android:statusBarColor">#FF121212</item>
        <item name="android:windowLightStatusBar">false</item>
    </style>
</resources>