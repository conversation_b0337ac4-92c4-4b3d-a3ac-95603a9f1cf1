<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.grupo3.medrem">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
        android:name=".MedRemApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MedRem"
        android:usesCleartextTraffic="true"
        tools:targetApi="31">
        <activity
            android:name=".activities.SettingsActivity"
            android:exported="false" />
        <activity
            android:name=".activities.TermsActivity"
            android:exported="false" />
        <activity
            android:name=".activities.NewReminderActivity"
            android:exported="false" />
        <activity
            android:name=".activities.DashboardActivity"
            android:exported="false" />
        <activity
            android:name=".activities.RegisterActivity"
            android:exported="false" />
        <activity
            android:name=".activities.LoginActivity"
            android:exported="false" />
        <activity
            android:name=".activities.Onboarding4Activity"
            android:exported="false" />
        <activity
            android:name=".activities.Onboarding3Activity"
            android:exported="false" />
        <activity
            android:name=".activities.Onboarding2Activity"
            android:exported="false" />
        <activity
            android:name=".activities.SplashActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activities.MainActivity"
            android:exported="false" />
    </application>

</manifest>